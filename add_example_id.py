#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为初中_最终_填充后.md文件添加例句id列的脚本

规则：
- 当某个例词的词义id未出现重复时，请标注为1
- 当某个例词的词义id出现重复时，请按1、2、3的递增序号逐渐递增
"""

import re
from collections import defaultdict

def process_markdown_table(input_file, output_file):
    """
    处理markdown表格，添加例句id列
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
    """
    
    # 读取文件内容
    with open(input_file, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 找到表格开始位置
    table_start = -1
    for i, line in enumerate(lines):
        if line.strip().startswith('| 序号 |'):
            table_start = i
            break
    
    if table_start == -1:
        print("未找到表格开始位置")
        return
    
    # 解析表格数据
    data_rows = []
    header_line = lines[table_start]
    separator_line = lines[table_start + 1]
    
    # 统计每个例词的词义id出现次数
    word_meaning_count = defaultdict(lambda: defaultdict(int))
    
    # 第一遍扫描：统计词义id出现次数
    for i in range(table_start + 2, len(lines)):
        line = lines[i].strip()
        if not line or not line.startswith('|'):
            break
        
        # 解析表格行
        cells = [cell.strip() for cell in line.split('|')[1:-1]]  # 去掉首尾空元素
        if len(cells) >= 5:  # 至少要有序号、例词、读音、词义、词义id
            word = cells[1]  # 例词
            meaning_id = cells[4]  # 词义id
            word_meaning_count[word][meaning_id] += 1
    
    # 第二遍扫描：生成例句id
    word_meaning_current = defaultdict(lambda: defaultdict(int))
    
    # 修改表头
    new_header = header_line.rstrip() + " 例句id |\n"
    new_separator = separator_line.rstrip() + "---|\n"
    
    # 处理数据行
    new_lines = lines[:table_start] + [new_header, new_separator]
    
    for i in range(table_start + 2, len(lines)):
        line = lines[i].strip()
        if not line or not line.startswith('|'):
            # 如果遇到非表格行，添加剩余的行并结束
            new_lines.extend(lines[i:])
            break
        
        # 解析表格行
        cells = [cell.strip() for cell in line.split('|')[1:-1]]  # 去掉首尾空元素
        if len(cells) >= 5:  # 至少要有序号、例词、读音、词义、词义id
            word = cells[1]  # 例词
            meaning_id = cells[4]  # 词义id
            
            # 计算例句id
            if word_meaning_count[word][meaning_id] == 1:
                example_id = "1"
            else:
                word_meaning_current[word][meaning_id] += 1
                example_id = str(word_meaning_current[word][meaning_id])
            
            # 重构行，添加例句id列
            new_line = "| " + " | ".join(cells) + f" | {example_id} |\n"
            new_lines.append(new_line)
        else:
            # 如果行格式不正确，保持原样
            new_lines.append(line + "\n")
    
    # 写入新文件
    with open(output_file, 'w', encoding='utf-8') as f:
        f.writelines(new_lines)
    
    print(f"处理完成！输出文件：{output_file}")
    print(f"共处理了 {len([l for l in new_lines if l.strip().startswith('|') and '序号' not in l and '---' not in l])} 行数据")

def main():
    input_file = "初中_最终_填充后.md"
    output_file = "初中_最终_填充后_带例句id.md"
    
    try:
        process_markdown_table(input_file, output_file)
    except FileNotFoundError:
        print(f"错误：找不到输入文件 {input_file}")
    except Exception as e:
        print(f"处理过程中出现错误：{e}")

if __name__ == "__main__":
    main()
